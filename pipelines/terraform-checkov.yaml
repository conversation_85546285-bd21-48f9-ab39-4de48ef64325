jobs:
- job: checkovJob
  pool:
    name: DEV-AksPool-centralagent-Deploy
  variables:
    - group: pr-validate    
  steps:
  - checkout: self
  - bash: |
      if [[ -f "~/.gitconfig" ]]; then
        rm ~/.gitconfig
      fi
      git config --global url."https://ADOS-OTPHU-01:$(GZ_PAT)@dev.azure.com".insteadOf https://<EMAIL>
      echo "===== git config ====="
      cat ~/.gitconfig
      set -e
      pip install pip --upgrade
      pip install checkov --upgrade
      pip install jq  # JSON feldolgozáshoz
      export GITHUB_PAT="$(GZ_PAT)"

      # Összesített számlálók inicializálása
      TOTAL_TESTS=0
      TOTAL_PASSED=0
      TOTAL_FAILED=0
      TOTAL_SKIPPED=0
      MODULES_PROCESSED=0

      echo "=== Starting Checkov Security Scan ==="

      for mod in `find . -name anchor.module -exec dirname {} \;`
      do
        INIT_DIR=`pwd`
        cd $mod/examples
        MODULE_NAME=$(basename "$mod")
        echo "Processing module: $MODULE_NAME"

        for dir in $(ls -d */)
        do
          MODULE_DIR=`pwd`
          dir=`echo $dir | tr -d '/'`
          cd $dir

          echo "  Scanning example: $dir"

          checkov -d . -o cli -o json -o junitxml \
            --output-file-path console,checkov-results_${dir}.json,checkov-results_${dir}.xml \
            --download-external-modules true || CHECKOV_EXIT_CODE=$?

          if [[ -f "checkov-results_${dir}.json" ]]; then
            echo "    Analyzing results for $dir..."

            passed=$(jq -r '.summary.passed // 0' "checkov-results_${dir}.json")
            failed=$(jq -r '.summary.failed // 0' "checkov-results_${dir}.json")
            skipped=$(jq -r '.summary.skipped // 0' "checkov-results_${dir}.json")
            total=$((passed + failed + skipped))

            echo "    Results: $total total, $passed passed, $failed failed, $skipped skipped"

            if [[ $failed -gt 0 ]]; then
              echo "    Failed checks:"
              jq -r '.results.failed_checks[]? | "      - \(.check_id): \(.check_name) (\(.file_path):\(.file_line_range[0]))"' "checkov-results_${dir}.json" | head -10
              if [[ $(jq '.results.failed_checks | length' "checkov-results_${dir}.json") -gt 10 ]]; then
                echo "      ... and $(($(jq '.results.failed_checks | length' "checkov-results_${dir}.json") - 10)) more"
              fi
            fi

            TOTAL_TESTS=$((TOTAL_TESTS + total))
            TOTAL_PASSED=$((TOTAL_PASSED + passed))
            TOTAL_FAILED=$((TOTAL_FAILED + failed))
            TOTAL_SKIPPED=$((TOTAL_SKIPPED + skipped))
            MODULES_PROCESSED=$((MODULES_PROCESSED + 1))

            if [[ -f "checkov-results_${dir}.xml" ]]; then
              sed -i 's/terraform scan/Checkov scan - '"$MODULE_NAME/$dir"'/' "checkov-results_${dir}.xml"
            fi
          else
            echo "    Warning: No JSON results file found for $dir"
          fi

          cd ${MODULE_DIR}
        done
        cd ${INIT_DIR}
      done

      echo ""
      echo "=== CHECKOV SECURITY SCAN SUMMARY ==="
      echo "Modules processed: $MODULES_PROCESSED"
      echo "Total checks: $TOTAL_TESTS"
      echo "Passed: $TOTAL_PASSED"
      echo "Failed: $TOTAL_FAILED"
      echo "Skipped: $TOTAL_SKIPPED"

      if [[ $TOTAL_TESTS -gt 0 ]]; then
        SUCCESS_RATE=$(echo "scale=2; $TOTAL_PASSED * 100 / $TOTAL_TESTS" | bc -l 2>/dev/null || echo "0")
        echo "Success rate: ${SUCCESS_RATE}%"
      else
        SUCCESS_RATE="0"
        echo "Success rate: N/A (no tests found)"
      fi

      echo "##vso[task.setvariable variable=CheckovTotalTests;isOutput=true]$TOTAL_TESTS"
      echo "##vso[task.setvariable variable=CheckovPassedTests;isOutput=true]$TOTAL_PASSED"
      echo "##vso[task.setvariable variable=CheckovFailedTests;isOutput=true]$TOTAL_FAILED"
      echo "##vso[task.setvariable variable=CheckovSkippedTests;isOutput=true]$TOTAL_SKIPPED"
      echo "##vso[task.setvariable variable=CheckovSuccessRate;isOutput=true]$SUCCESS_RATE"
      echo "##vso[task.setvariable variable=CheckovModulesProcessed;isOutput=true]$MODULES_PROCESSED"

      if [[ $TOTAL_FAILED -gt 0 ]]; then
        echo "##vso[task.logissue type=error]Checkov found $TOTAL_FAILED security issues across $MODULES_PROCESSED modules!"
        echo "##vso[task.logissue type=warning]Check the detailed results in the Test Results tab"
      else
        echo "##vso[task.logissue type=warning]Checkov scan completed successfully - no security issues found!"
      fi

      echo "=== End of Checkov Security Scan ==="
    displayName: "Checkovscan with JSON Analysis"
    name: "checkov_scan"
  - task: PublishTestResults@2
    inputs:
      testResultsFormat: 'JUnit'
      testResultsFiles: '**/checkov-results_*.xml'
      testRunTitle: 'Checkov Security Scan Results'
      mergeTestResults: false
      publishRunAttachement: true
      failTaskOnFailedTests: true  # Ez okozza a pipeline hibát, ha vannak sikertelen tesztek
    displayName: "Publish Checkov Test Results"

  # Összefoglaló jelentés készítése
  - bash: |
      echo "=== Creating Checkov Summary Report ==="

      # Build summary markdown készítése
      cat > checkov_summary.md << EOF
      # 🔒 Checkov Security Scan Results

      ## Summary
      | Metric | Value |
      |--------|-------|
      | Modules Processed | $(checkov_scan.CheckovModulesProcessed) |
      | Total Security Checks | $(checkov_scan.CheckovTotalTests) |
      | ✅ Passed | $(checkov_scan.CheckovPassedTests) |
      | ❌ Failed | $(checkov_scan.CheckovFailedTests) |
      | ⏭️ Skipped | $(checkov_scan.CheckovSkippedTests) |
      | 📊 Success Rate | $(checkov_scan.CheckovSuccessRate)% |

      ## Status
      EOF

      if [[ $(checkov_scan.CheckovFailedTests) -gt 0 ]]; then
        cat >> checkov_summary.md << EOF
      ⚠️ **Security issues found!** Please review the failed checks in the Test Results tab.

      ### Next Steps
      1. Review the detailed results in the **Tests** tab
      2. Fix the identified security issues
      3. Re-run the pipeline to verify fixes
      EOF
      else
        cat >> checkov_summary.md << EOF
      ✅ **All security checks passed!** No issues found.
      EOF
      fi

      # Summary feltöltése a pipeline-ba
      echo "##vso[task.uploadsummary]$(pwd)/checkov_summary.md"

      echo "Summary report created and uploaded to pipeline"
    displayName: "Create Checkov Summary Report"
    condition: always()  # Mindig fusson le, még hiba esetén is