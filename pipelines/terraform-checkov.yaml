jobs:
- job: checkovJob
  pool:
    name: DEV-AksPool-centralagent-Deploy

  steps:
  - checkout: self
  - bash: |
      set -e
      pip install pip --upgrade
      pip install checkov --upgrade
      pip install jq  # JSON feldolgozáshoz

      TOTAL_TESTS=0
      TOTAL_PASSED=0
      TOTAL_FAILED=0
      TOTAL_SKIPPED=0
      MODULES_PROCESSED=0

      echo "=== Starting Checkov Security Scan ==="

      for mod in `find . -name anchor.module -exec dirname {} \;`
      do
        INIT_DIR=`pwd`
        MODULE_NAME=$(basename "$mod")
        echo "Processing module: $MODULE_NAME"

        # Test the main module first
        cd $mod
        echo "  Scanning main module: $MODULE_NAME"

        checkov -d . -o cli -o json -o junitxml \
          --output-file-path console,checkov-results_main.json,checkov-results_main.xml || CHECKOV_EXIT_CODE=$?

        if [[ -f "checkov-results_main.json" ]]; then
          echo "    Analyzing results for main module..."

          passed=$(jq -r '.summary.passed // 0' "checkov-results_main.json")
          failed=$(jq -r '.summary.failed // 0' "checkov-results_main.json")
          skipped=$(jq -r '.summary.skipped // 0' "checkov-results_main.json")
          total=$((passed + failed + skipped))

          echo "    Results: $total total, $passed passed, $failed failed, $skipped skipped"

          if [[ $failed -gt 0 ]]; then
            echo "    Failed checks:"
            jq -r '.results.failed_checks[]? | "      - \(.check_id): \(.check_name) (\(.file_path):\(.file_line_range[0]))"' "checkov-results_main.json" | head -10
            if [[ $(jq '.results.failed_checks | length' "checkov-results_main.json") -gt 10 ]]; then
              echo "      ... and $(($(jq '.results.failed_checks | length' "checkov-results_main.json") - 10)) more"
            fi
          fi

          TOTAL_TESTS=$((TOTAL_TESTS + total))
          TOTAL_PASSED=$((TOTAL_PASSED + passed))
          TOTAL_FAILED=$((TOTAL_FAILED + failed))
          TOTAL_SKIPPED=$((TOTAL_SKIPPED + skipped))
          MODULES_PROCESSED=$((MODULES_PROCESSED + 1))

          if [[ -f "checkov-results_main.xml" ]]; then
            sed -i 's/terraform scan/Checkov scan - '"$MODULE_NAME/main"'/' "checkov-results_main.xml"
          fi
        else
          echo "    Warning: No JSON results file found for main module"
        fi

        # Test examples if they exist
        if [[ -d "examples" ]]; then
          cd examples
          for dir in $(ls -d */ 2>/dev/null || true)
          do
            MODULE_DIR=`pwd`
            dir=`echo $dir | tr -d '/'`
            cd $dir

            echo "  Scanning example: $dir"

            checkov -d . -o cli -o json -o junitxml \
              --output-file-path console,checkov-results_${dir}.json,checkov-results_${dir}.xml || CHECKOV_EXIT_CODE=$?

          if [[ -f "checkov-results_${dir}.json" ]]; then
            echo "    Analyzing results for $dir..."

            passed=$(jq -r '.summary.passed // 0' "checkov-results_${dir}.json")
            failed=$(jq -r '.summary.failed // 0' "checkov-results_${dir}.json")
            skipped=$(jq -r '.summary.skipped // 0' "checkov-results_${dir}.json")
            total=$((passed + failed + skipped))

            echo "    Results: $total total, $passed passed, $failed failed, $skipped skipped"

            if [[ $failed -gt 0 ]]; then
              echo "    Failed checks:"
              jq -r '.results.failed_checks[]? | "      - \(.check_id): \(.check_name) (\(.file_path):\(.file_line_range[0]))"' "checkov-results_${dir}.json" | head -10
              if [[ $(jq '.results.failed_checks | length' "checkov-results_${dir}.json") -gt 10 ]]; then
                echo "      ... and $(($(jq '.results.failed_checks | length' "checkov-results_${dir}.json") - 10)) more"
              fi
            fi

            TOTAL_TESTS=$((TOTAL_TESTS + total))
            TOTAL_PASSED=$((TOTAL_PASSED + passed))
            TOTAL_FAILED=$((TOTAL_FAILED + failed))
            TOTAL_SKIPPED=$((TOTAL_SKIPPED + skipped))
            MODULES_PROCESSED=$((MODULES_PROCESSED + 1))

            if [[ -f "checkov-results_${dir}.xml" ]]; then
              sed -i 's/terraform scan/Checkov scan - '"$MODULE_NAME/$dir"'/' "checkov-results_${dir}.xml"
            fi
          else
            echo "    Warning: No JSON results file found for $dir"
          fi

            cd ${MODULE_DIR}
          done
        fi

        cd ${INIT_DIR}
      done

      echo ""
      echo "=== CHECKOV SECURITY SCAN SUMMARY ==="
      echo "Modules processed: $MODULES_PROCESSED"
      echo "Total checks: $TOTAL_TESTS"
      echo "Passed: $TOTAL_PASSED"
      echo "Failed: $TOTAL_FAILED"
      echo "Skipped: $TOTAL_SKIPPED"

      if [[ $TOTAL_TESTS -gt 0 ]]; then
        SUCCESS_RATE=$(echo "scale=2; $TOTAL_PASSED * 100 / $TOTAL_TESTS" | bc -l 2>/dev/null || echo "0")
        echo "Success rate: ${SUCCESS_RATE}%"
      else
        SUCCESS_RATE="0"
        echo "Success rate: N/A (no tests found)"
      fi

      echo "##vso[task.setvariable variable=CheckovTotalTests;isOutput=true]$TOTAL_TESTS"
      echo "##vso[task.setvariable variable=CheckovPassedTests;isOutput=true]$TOTAL_PASSED"
      echo "##vso[task.setvariable variable=CheckovFailedTests;isOutput=true]$TOTAL_FAILED"
      echo "##vso[task.setvariable variable=CheckovSkippedTests;isOutput=true]$TOTAL_SKIPPED"
      echo "##vso[task.setvariable variable=CheckovSuccessRate;isOutput=true]$SUCCESS_RATE"
      echo "##vso[task.setvariable variable=CheckovModulesProcessed;isOutput=true]$MODULES_PROCESSED"

      if [[ $TOTAL_FAILED -gt 0 ]]; then
        echo "##vso[task.logissue type=error]Checkov found $TOTAL_FAILED security issues across $MODULES_PROCESSED modules!"
        echo "##vso[task.logissue type=warning]Check the detailed results in the Test Results tab"
      else
        echo "##vso[task.logissue type=warning]Checkov scan completed successfully - no security issues found!"
      fi

      echo "=== End of Checkov Security Scan ==="
    displayName: "Checkovscan with JSON Analysis"
    name: "checkov_scan"
  - task: PublishTestResults@2
    inputs:
      testResultsFormat: 'JUnit'
      testResultsFiles: '**/checkov-results_*.xml'
      testRunTitle: 'Checkov Security Scan Results'
      mergeTestResults: false
      publishRunAttachement: true
      failTaskOnFailedTests: true  # Ez okozza a pipeline hibát, ha vannak sikertelen tesztek
    displayName: "Publish Checkov Test Results"

  # Összefoglaló jelentés készítése
  - bash: |
      echo "=== Creating Checkov Summary Report ==="

      # Build summary markdown készítése
      cat > checkov_summary.md << EOF
      # 🔒 Checkov Security Scan Results

      ## Summary
      | Metric | Value |
      |--------|-------|
      | Modules Processed | $(checkov_scan.CheckovModulesProcessed) |
      | Total Security Checks | $(checkov_scan.CheckovTotalTests) |
      | ✅ Passed | $(checkov_scan.CheckovPassedTests) |
      | ❌ Failed | $(checkov_scan.CheckovFailedTests) |
      | ⏭️ Skipped | $(checkov_scan.CheckovSkippedTests) |
      | 📊 Success Rate | $(checkov_scan.CheckovSuccessRate)% |

      ## Status
      EOF

      if [[ $(checkov_scan.CheckovFailedTests) -gt 0 ]]; then
        cat >> checkov_summary.md << EOF
      ⚠️ **Security issues found!** Please review the failed checks in the Test Results tab.

      ### Next Steps
      1. Review the detailed results in the **Tests** tab
      2. Fix the identified security issues
      3. Re-run the pipeline to verify fixes
      EOF
      else
        cat >> checkov_summary.md << EOF
      ✅ **All security checks passed!** No issues found.
      EOF
      fi

      # Summary feltöltése a pipeline-ba
      echo "##vso[task.uploadsummary]$(pwd)/checkov_summary.md"

      echo "Summary report created and uploaded to pipeline"
    displayName: "Create Checkov Summary Report"
    condition: always()  # Mindig fusson le, még hiba esetén is